# AISpeak 环境变量配置模板
# 复制此文件为 .env 并填入实际配置值

# ===========================
# 数据库配置
# ===========================
# MySQL 数据库连接字符串
# 格式: mysql://用户名:密码@主机:端口/数据库名
DATABASE_URL=mysql://aispeak:your_password@localhost:3306/aispeak_db

# 是否开启 SQL 日志（调试用，生产环境建议设为 false）
SQL_ECHO=false

# ===========================
# 安全配置
# ===========================
# JWT 密钥（至少32个字符，请使用随机生成的密钥）
TOKEN_SECRET=your-secret-key-at-least-32-characters

# Token 过期时间（秒）
# 86400 = 24小时
TOKEN_EXPIRE_TIME=86400

# ===========================
# 微信小程序配置
# ===========================
# 微信小程序 AppID
WECHAT_APP_ID=your_wechat_app_id

# 微信小程序 AppSecret
WECHAT_APP_SECRET=your_wechat_app_secret

# 微信服务器地址
WE_CHAT_SERVER_URL=https://api.weixin.qq.com

# 微信公众平台 AppID（用于分享签名）
WX_APP_ID=your_wx_app_id

# ===========================
# AI 服务配置
# ===========================
# AI 服务提供商 (openai/zhipu/custom)
AI_SERVER=openai

# AI 服务名称
AI_NAME=OpenAI

# OpenAI 配置
CHAT_GPT_KEY=your_openai_api_key
CHAT_GPT_MODEL=gpt-3.5-turbo
# API 代理地址（如果需要）
CHAT_GPT_PROXY=https://api.openai.com

# ===========================
# 微软语音服务配置
# ===========================
# Azure 语音服务密钥
AZURE_KEY=your_azure_speech_key

# Azure 服务区域
AZURE_REGIEON=eastasia

# ===========================
# 阿里云 OSS 配置（可选）
# ===========================
# 如果使用阿里云 OSS 存储文件，请配置以下参数
ALIBABA_CLOUD_ACCESS_KEY_ID=your_oss_access_key_id
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_oss_access_key_secret

# ===========================
# 文件存储配置
# ===========================
# 临时文件保存路径
TEMP_SAVE_FILE_PATH=/aispeak-server/files

# ===========================
# API 配置
# ===========================
# API 前缀
API_PREFIX=/api/v1

# ===========================
# 部署配置
# ===========================
# 域名（用于前端配置）
DOMAIN=your-domain.com

# SSL 证书邮箱（用于 Let's Encrypt）
SSL_EMAIL=<EMAIL>

# ===========================
# 日志配置
# ===========================
# 日志级别 (debug/info/warning/error)
LOG_LEVEL=info

# ===========================
# 性能配置
# ===========================
# Gunicorn Worker 数量
WORKERS=4

# ===========================
# 备份配置
# ===========================
# 备份保留天数
BACKUP_RETENTION_DAYS=7

# 备份存储路径
BACKUP_PATH=/opt/backups/aispeak