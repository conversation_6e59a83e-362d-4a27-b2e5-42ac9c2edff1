{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(pip3 list:*)", "<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(rm:*)", "<PERSON><PERSON>(python -m alembic revision:*)", "<PERSON><PERSON>(source:*)", "Bash(alembic revision:*)", "Bash(alembic upgrade:*)", "Bash(python -m pytest tests/test-classes-python.sh -v)", "<PERSON><PERSON>(chmod:*)", "Bash(python scripts/update_class_codes.py:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(poetry run alembic:*)", "Bash(conda:*)", "Bash(/Users/<USER>/opt/anaconda3/envs/fluent-ai/bin/alembic -c alembic.prod.ini revision --autogenerate -m \"db sync\")", "Bash(/Users/<USER>/opt/anaconda3/envs/fluent-ai/bin/python verify_migration.py)", "Bash(grep:*)", "Bash(npm run dev:h5:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(./venv/bin/python -m alembic revision:*)", "Bash(./venv/bin/python -m alembic:*)", "Bash(npm run type-check:*)"], "deny": []}}