# 后端API需求文档

## 1. 系统设置接口

### 获取系统设置
```
GET /api/v1/sys/settings

Response:
{
  "code": 0,
  "data": {
    "features": {
      "showTextbookModule": false,  // 是否显示教材模块
      "enableManualInput": true     // 是否启用手动输入（已废弃）
    },
    // 其他系统设置...
  }
}
```

### 更新系统设置
```
POST /api/v1/sys/settings
{
  "features": {
    "showTextbookModule": true
  }
}

Response:
{
  "code": 0,
  "message": "设置更新成功"
}
```

## 2. 内容搜索接口

### 搜索单词或句子
```
POST /api/v1/task/search-content
{
  "keyword": "apple",
  "type": "word"  // "word" | "sentence"
}

Response (搜索单词):
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "word": "apple",
      "chinese_meaning": "苹果",
      "phonetic": "[ˈæpl]"
    },
    {
      "id": 2,
      "word": "pineapple",
      "chinese_meaning": "菠萝",
      "phonetic": "[ˈpaɪnæpl]"
    }
  ]
}

Response (搜索句子):
{
  "code": 0,
  "data": [
    {
      "id": 101,
      "english": "I like apples.",
      "chinese": "我喜欢苹果。"
    },
    {
      "id": 102,
      "english": "An apple a day keeps the doctor away.",
      "chinese": "一天一苹果，医生远离我。"
    }
  ]
}

Response (无结果):
{
  "code": 0,
  "data": []
}
```

## 3. 任务创建接口更新

### 支持搜索模式的内容
```
POST /api/v1/task
{
  "title": "水果单词学习",
  "task_type": "dictation",
  "class_id": 1,
  "deadline": "2024-12-31T18:00:00Z",
  "contents": [
    {
      "content_type": "dictation",
      "generate_mode": "search",  // 新增：搜索模式
      "ref_book_id": null,
      "ref_lesson_id": null,
      "selected_word_ids": [1, 2, 3],  // 搜索选中的单词ID
      "selected_sentence_ids": [],     // 搜索选中的句子ID
      "points": 100
    }
  ]
}
```

### generate_mode 说明
- `auto`: 从教材自动生成（原有）
- `search`: 从搜索结果选择
- `manual`: 手动输入（已废弃）

## 实现建议

1. **搜索功能**
   - 支持模糊搜索（LIKE '%keyword%'）
   - 支持中英文搜索
   - 可以限制返回结果数量（如最多50个）
   - 按相关度或使用频率排序

2. **功能开关**
   - 可以存储在数据库的系统配置表中
   - 支持动态更新，无需重启服务
   - 可以考虑添加更多功能开关

3. **权限控制**
   - 系统设置更新接口需要管理员权限
   - 搜索接口需要教师权限
   - 任务创建接口需要教师权限