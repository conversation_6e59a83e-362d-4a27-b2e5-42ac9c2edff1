# 任务创建功能说明

## 功能概述

提供了灵活的任务创建方式：
- **搜索模式**：通过关键词搜索系统中的单词和句子
- **教材模式**：从教材库选择内容（需要开启功能开关）

## 功能开关配置

### 后端配置
系统通过 `/sys/settings` API 返回功能配置：

```json
{
  "features": {
    "showTextbookModule": false,  // 是否显示教材模块
    "enableManualInput": true     // 是否启用手动输入
  }
}
```

### 前端配置文件
位置：`src/config/features.ts`

- 支持从后端动态获取配置
- 支持本地缓存，确保离线时功能可用
- 默认隐藏教材模块，使用搜索模式

## 使用说明

### 1. 关键词搜索模式（默认）

通过输入关键词搜索单词或句子：

#### 搜索单词
- 输入关键词（如 "app"）搜索相关单词
- 系统返回包含该关键词的单词列表
- 可以选择多个单词加入任务
- **注意：只能选择系统中已有的单词，搜索不到的单词无法使用**

#### 搜索句子  
- 输入关键词（如 "how"）搜索相关句子
- 系统返回包含该关键词的句子列表
- 可以选择多个句子加入任务
- **注意：只能选择系统中已有的句子，搜索不到的句子无法使用**

### 2. 教材选择模式

当 `showTextbookModule` 为 `true` 时，用户可以在以下两种模式间切换：
- 搜索内容：通过关键词搜索内容
- 选择教材：从教材库选择内容

### 3. 教材模块访问控制

当教材模块被禁用时：
- 访问教材页面会提示"教材功能暂未开放"
- 自动跳转到任务页面
- 任务创建只显示搜索选项

## 数据结构

### 搜索模式的任务内容
```typescript
{
  content_type: "dictation",
  generate_mode: "search",
  ref_book_id: null,
  ref_lesson_id: null,
  selected_word_ids: [1, 2, 3], // 选中的单词ID
  selected_sentence_ids: [], // 选中的句子ID
  manual_content: {
    words: [...], // 选中的单词详情
    sentences: [...] // 选中的句子详情
  }
}
```


## 审核通过后的操作

1. 后端更新配置：将 `showTextbookModule` 设置为 `true`
2. 前端自动获取最新配置
3. 用户可以访问教材模块和选择教材创建任务

## 注意事项

1. 搜索模式只能选择系统中已存在的内容
2. 搜索模式需要后端提供搜索API接口
3. 教材模式和搜索模式的数据结构不同，后端需要分别处理
4. 功能配置有本地缓存，确保网络不稳定时功能可用

## 待实现的后端API

### 内容搜索接口
```
POST /api/task/search-content
{
  "keyword": "apple",
  "type": "word" | "sentence"
}

Response:
{
  "data": [
    {
      "id": 1,
      "word": "apple",
      "chinese_meaning": "苹果"
    }
  ]
}
```