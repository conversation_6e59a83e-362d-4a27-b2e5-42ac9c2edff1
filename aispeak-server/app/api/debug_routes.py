from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.db import get_db
from app.models.response import ApiResponse

router = APIRouter()

@router.get("/task/{task_id}/debug", response_model=ApiResponse)
def debug_task_contents(
    task_id: int,
    db: Session = Depends(get_db)
) -> ApiResponse:
    """
    调试端点：查看任务的详细内容
    """
    try:
        from app.db.task_entities import Task, TaskContent
        
        # 查询任务
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            return ApiResponse.error("任务不存在")
        
        # 查询所有任务内容
        task_contents = db.query(TaskContent).filter(
            TaskContent.task_id == task_id
        ).all()
        
        contents_data = []
        for content in task_contents:
            contents_data.append({
                "id": content.id,
                "content_type": content.content_type,
                "generate_mode": content.generate_mode,
                "ref_book_id": content.ref_book_id,
                "ref_lesson_id": content.ref_lesson_id,
                "selected_word_ids": content.selected_word_ids,
                "selected_sentence_ids": content.selected_sentence_ids,
                "points": content.points,
                "order_num": content.order_num,
                "created_at": content.created_at.isoformat() if content.created_at else None,
                "deleted_at": content.deleted_at.isoformat() if content.deleted_at else None
            })
        
        return ApiResponse.success({
            "task": {
                "id": task.id,
                "title": task.title,
                "task_type": task.task_type.value if task.task_type else None,
                "teacher_id": task.teacher_id,
                "class_id": task.class_id,
                "textbook_id": task.textbook_id,
                "lesson_id": task.lesson_id,
                "created_at": task.created_at.isoformat() if task.created_at else None
            },
            "contents": contents_data,
            "contents_count": len(contents_data)
        })
        
    except Exception as e:
        return ApiResponse.system_error(str(e))