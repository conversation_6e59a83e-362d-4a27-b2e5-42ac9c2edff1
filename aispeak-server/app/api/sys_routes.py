import os
from fastapi import APIRouter, Depends, Request, UploadFile, File, Response
from sqlalchemy.orm import Session
from app.core import get_current_account
from app.db import get_db
from app.models.sys_models import *
from app.models.sys_models import SystemSettingsResponse, SystemSettingsUpdate
from app.models.response import ApiResponse
from app.services.sys_service import SysService
from app.config import Config
from app.core.utils import *

router = APIRouter()

@router.get("/languages/example")
def get_settings_languages_example(
    language: str,
    db: Session = Depends(get_db),
    account_id: str = Depends(get_current_account),
):
    """获取支持的语言"""
    sys_service = SysService(db)
    return ApiResponse(
        data=sys_service.get_settings_languages_example(language, account_id)
    )    
    
# 获取语言下支持的角色
@router.get("/sys/roles")
def get_settings_roles(
    locale: str,
    db: Session = Depends(get_db),
    account_id: str = Depends(get_current_account),
):
    """获取支持的角色"""
    sys_service = SysService(db)
    return ApiResponse(data=sys_service.get_settings_roles(locale, account_id))


@router.get("/sys/languages")
def get_settings_languages(
    db: Session = Depends(get_db), account_id: str = Depends(get_current_account)
):
    """获取支持的语言"""
    sys_service = SysService(db)
    return ApiResponse(data=sys_service.get_settings_languages(account_id))    


import logging

logger = logging.getLogger(__name__)

@router.post("/voices/upload")
def voice_upload_api(
    db: Session = Depends(get_db),
    file: UploadFile = File(...),
    account_id: str = Depends(get_current_account),
):
    """上传语音文件"""
    try:
        logger.info(f"Voice upload started. Account: {account_id}")
        logger.info(f"File info - Name: {file.filename}, Size: {file.size}, Type: {file.content_type}")
        
        sys_service = SysService(db)
        result = sys_service.voice_upload(file, account_id)
        
        logger.info(f"Voice upload completed. Account: {account_id}, Result: {result}")
        return ApiResponse(data=result)
        
    except Exception as e:
        logger.error(f"Voice upload failed. Account: {account_id}, Error: {str(e)}", exc_info=True)
        return ApiResponse(
            success=False,
            message="文件上传失败",
            error=str(e)
        )


@router.get("/voices/{file_name}")
def get_file(file_name: str, response: Response):
    """获取文件"""
    file_path = voice_file_get_path(file_name)
    # 判断文件是否存在
    if os.path.isfile(file_path):
        with open(file_path, "rb") as file:
            contents = file.read()
            response.headers["Content-Type"] = "application/octet-stream"
            response.headers[
                "Content-Disposition"
            ] = f"attachment; filename={os.path.basename(file_path)}"
            return Response(content=contents, media_type="application/octet-stream")
    else:
        return {"error": f"File {file_name} not found."}

@router.post("/sys/feedback")
def add_feedback(
    dto: FeedbackDTO,
    db: Session = Depends(get_db),
    account_id: str = Depends(get_current_account),
):
    """用户反馈"""
    sys_service = SysService(db)
    sys_service.add_feedback(dto, account_id)
    return ApiResponse(data="SUCCESS")


@router.get("/sys/settings")
def get_system_settings(
    db: Session = Depends(get_db),
    account_id: str = Depends(get_current_account),
):
    """获取系统设置"""
    sys_service = SysService(db)
    settings = sys_service.get_system_settings()
    return ApiResponse(data=settings)


@router.post("/sys/settings")
def update_system_settings(
    settings: SystemSettingsUpdate,
    db: Session = Depends(get_db),
    account_id: str = Depends(get_current_account),
):
    """更新系统设置（需要管理员权限）"""
    # TODO: 检查是否是管理员权限
    sys_service = SysService(db)
    updated_settings = sys_service.update_system_settings(settings)
    return ApiResponse(data=updated_settings)
