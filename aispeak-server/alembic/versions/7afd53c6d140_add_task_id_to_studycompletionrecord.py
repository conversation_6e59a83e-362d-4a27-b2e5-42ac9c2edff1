"""Add task_id to StudyCompletionRecord

Revision ID: 7afd53c6d140
Revises: bba7d6437a88
Create Date: 2025-08-01 00:22:57.032666

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '7afd53c6d140'
down_revision: Union[str, None] = 'bba7d6437a88'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('study_completion_records', sa.Column('task_id', sa.Integer(), nullable=True, comment='任务ID'))
    op.alter_column('study_completion_records', 'book_id',
               existing_type=mysql.VARCHAR(length=50),
               nullable=True,
               existing_comment='书籍ID')
    op.create_index('idx_user_task', 'study_completion_records', ['user_id', 'task_id', 'type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_user_task', table_name='study_completion_records')
    op.alter_column('study_completion_records', 'book_id',
               existing_type=mysql.VARCHAR(length=50),
               nullable=False,
               existing_comment='书籍ID')
    op.drop_column('study_completion_records', 'task_id')
    # ### end Alembic commands ###
