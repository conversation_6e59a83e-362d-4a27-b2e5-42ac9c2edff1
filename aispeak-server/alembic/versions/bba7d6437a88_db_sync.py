"""db sync

Revision ID: bba7d6437a88
Revises: 
Create Date: 2025-07-30 01:21:31.651706

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'bba7d6437a88'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('class_students',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('class_id', sa.Integer(), nullable=False, comment='班级ID'),
    sa.Column('student_id', sa.String(length=80), nullable=False, comment='学生ID'),
    sa.Column('join_date', sa.DateTime(), nullable=False, comment='加入日期'),
    sa.Column('leave_date', sa.DateTime(), nullable=True, comment='离开日期'),
    sa.Column('status', sa.String(length=20), nullable=True, comment='状态'),
    sa.Column('role', sa.String(length=50), nullable=True, comment='班级角色'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_class_students_class_id'), 'class_students', ['class_id'], unique=False)
    op.create_index(op.f('ix_class_students_id'), 'class_students', ['id'], unique=False)
    op.create_index(op.f('ix_class_students_status'), 'class_students', ['status'], unique=False)
    op.create_index(op.f('ix_class_students_student_id'), 'class_students', ['student_id'], unique=False)
    op.create_table('class_teachers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('class_id', sa.Integer(), nullable=False, comment='班级ID'),
    sa.Column('teacher_id', sa.String(length=80), nullable=False, comment='教师ID'),
    sa.Column('join_date', sa.DateTime(), nullable=False, comment='加入日期'),
    sa.Column('leave_date', sa.DateTime(), nullable=True, comment='离开日期'),
    sa.Column('status', sa.String(length=20), nullable=True, comment='状态'),
    sa.Column('role', sa.String(length=50), nullable=True, comment='教师角色'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_class_teachers_class_id'), 'class_teachers', ['class_id'], unique=False)
    op.create_index(op.f('ix_class_teachers_id'), 'class_teachers', ['id'], unique=False)
    op.create_index(op.f('ix_class_teachers_status'), 'class_teachers', ['status'], unique=False)
    op.create_index(op.f('ix_class_teachers_teacher_id'), 'class_teachers', ['teacher_id'], unique=False)
    op.create_table('classes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=200), nullable=False, comment='班级名称'),
    sa.Column('grade_level', sa.String(length=50), nullable=False, comment='年级'),
    sa.Column('subject', sa.String(length=50), nullable=True, comment='主教学科'),
    sa.Column('school_name', sa.String(length=200), nullable=True, comment='学校名称(可选)'),
    sa.Column('teacher_id', sa.String(length=80), nullable=False, comment='班主任ID'),
    sa.Column('class_code', sa.String(length=6), nullable=True, comment='班级码(6位唯一)'),
    sa.Column('status', sa.String(length=20), nullable=True, comment='状态'),
    sa.Column('description', sa.Text(), nullable=True, comment='班级描述'),
    sa.Column('max_students', sa.Integer(), nullable=False, comment='最大学生人数'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_classes_class_code'), 'classes', ['class_code'], unique=True)
    op.create_index(op.f('ix_classes_grade_level'), 'classes', ['grade_level'], unique=False)
    op.create_index(op.f('ix_classes_id'), 'classes', ['id'], unique=False)
    op.create_index(op.f('ix_classes_school_name'), 'classes', ['school_name'], unique=False)
    op.create_index(op.f('ix_classes_status'), 'classes', ['status'], unique=False)
    op.create_index(op.f('ix_classes_subject'), 'classes', ['subject'], unique=False)
    op.create_index(op.f('ix_classes_teacher_id'), 'classes', ['teacher_id'], unique=False)
    op.create_table('submissions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('student_task_id', sa.Integer(), nullable=False, comment='学生任务ID'),
    sa.Column('student_id', sa.String(length=80), nullable=True, comment='学生ID'),
    sa.Column('content_id', sa.Integer(), nullable=False, comment='内容ID'),
    sa.Column('response', sa.Text(), nullable=True, comment='回答内容'),
    sa.Column('media_files', sa.JSON(), nullable=True, comment='统一媒体文件'),
    sa.Column('is_correct', sa.Boolean(), nullable=True, comment='是否正确'),
    sa.Column('auto_score', sa.Integer(), nullable=True, comment='自动评分'),
    sa.Column('teacher_score', sa.Integer(), nullable=True, comment='教师评分'),
    sa.Column('feedback', sa.Text(), nullable=True, comment='单项反馈'),
    sa.Column('attempt_count', sa.Integer(), nullable=True, comment='尝试次数'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_submissions_content_id'), 'submissions', ['content_id'], unique=False)
    op.create_index(op.f('ix_submissions_id'), 'submissions', ['id'], unique=False)
    op.create_index(op.f('ix_submissions_is_correct'), 'submissions', ['is_correct'], unique=False)
    op.create_index(op.f('ix_submissions_student_id'), 'submissions', ['student_id'], unique=False)
    op.create_index(op.f('ix_submissions_student_task_id'), 'submissions', ['student_task_id'], unique=False)
    op.create_table('task_target',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('info_cn', sa.String(length=200), nullable=False),
    sa.Column('info_en', sa.String(length=200), nullable=True),
    sa.Column('lesson_id', sa.String(length=500), nullable=False),
    sa.Column('info_en_audio', sa.String(length=500), nullable=True),
    sa.Column('match_type', sa.Integer(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('tasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('teacher_id', sa.String(length=80), nullable=False, comment='教师ID'),
    sa.Column('class_id', sa.Integer(), nullable=False, comment='班级ID'),
    sa.Column('title', sa.String(length=200), nullable=False, comment='任务标题'),
    sa.Column('description', sa.Text(), nullable=True, comment='任务描述'),
    sa.Column('task_type', sa.Enum('DICTATION', 'SPELLING', 'PRONUNCIATION', 'SENTENCE_REPEAT', 'QUIZ', name='tasktype'), nullable=False, comment='任务类型'),
    sa.Column('subject', sa.Enum('ENGLISH', 'CHINESE', 'MATH', 'SCIENCE', 'HISTORY', 'GEOGRAPHY', 'ART', 'MUSIC', 'PHYSICAL_EDUCATION', 'OTHER', name='subjecttype'), nullable=False, comment='所属学科'),
    sa.Column('deadline', sa.DateTime(), nullable=True, comment='截止时间'),
    sa.Column('status', sa.Enum('DRAFT', 'PUBLISHED', 'IN_PROGRESS', 'COMPLETED', 'ARCHIVED', name='taskstatus'), nullable=True, comment='任务状态'),
    sa.Column('allow_late_submission', sa.Boolean(), nullable=True, comment='允许迟交'),
    sa.Column('max_attempts', sa.Integer(), nullable=True, comment='最大尝试次数'),
    sa.Column('grading_criteria', sa.Text(), nullable=True, comment='评分标准'),
    sa.Column('total_points', sa.Integer(), nullable=False, comment='总分'),
    sa.Column('attachments', sa.JSON(), nullable=True, comment='附件信息'),
    sa.Column('textbook_id', sa.String(length=80), nullable=True, comment='关联教材ID'),
    sa.Column('lesson_id', sa.Integer(), nullable=True, comment='关联教学单元ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tasks_class_id'), 'tasks', ['class_id'], unique=False)
    op.create_index(op.f('ix_tasks_id'), 'tasks', ['id'], unique=False)
    op.create_index(op.f('ix_tasks_lesson_id'), 'tasks', ['lesson_id'], unique=False)
    op.create_index(op.f('ix_tasks_subject'), 'tasks', ['subject'], unique=False)
    op.create_index(op.f('ix_tasks_teacher_id'), 'tasks', ['teacher_id'], unique=False)
    op.create_index(op.f('ix_tasks_textbook_id'), 'tasks', ['textbook_id'], unique=False)
    op.create_table('task_contents',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('task_id', sa.Integer(), nullable=False, comment='任务ID'),
    sa.Column('content_type', sa.String(length=50), nullable=False, comment='内容类型'),
    sa.Column('generate_mode', sa.String(length=20), nullable=True, comment='生成模式'),
    sa.Column('ref_book_id', sa.String(length=50), nullable=True, comment='关联教材ID'),
    sa.Column('ref_lesson_id', sa.Integer(), nullable=True, comment='关联单元ID'),
    sa.Column('selected_word_ids', sa.JSON(), nullable=True, comment='手动选择的单词ID列表'),
    sa.Column('selected_sentence_ids', sa.JSON(), nullable=True, comment='手动选择的句子ID列表'),
    sa.Column('points', sa.Integer(), nullable=False, comment='分值'),
    sa.Column('metadata', sa.JSON(), nullable=True, comment='元数据'),
    sa.Column('order_num', sa.Integer(), nullable=False, comment='排序号'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['task_id'], ['tasks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_task_contents_content_type'), 'task_contents', ['content_type'], unique=False)
    op.create_index(op.f('ix_task_contents_id'), 'task_contents', ['id'], unique=False)
    op.create_index(op.f('ix_task_contents_ref_book_id'), 'task_contents', ['ref_book_id'], unique=False)
    op.create_index(op.f('ix_task_contents_ref_lesson_id'), 'task_contents', ['ref_lesson_id'], unique=False)
    op.create_index(op.f('ix_task_contents_task_id'), 'task_contents', ['task_id'], unique=False)
    op.drop_table('atlas_schema_revisions')
    op.add_column('account', sa.Column('user_role', sa.String(length=20), nullable=True, comment='用户身份角色：teacher/student'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('account', 'user_role')
    op.create_table('atlas_schema_revisions',
    sa.Column('version', mysql.VARCHAR(collation='utf8mb4_bin', length=255), nullable=False),
    sa.Column('description', mysql.VARCHAR(collation='utf8mb4_bin', length=255), nullable=False),
    sa.Column('type', mysql.BIGINT(unsigned=True), server_default=sa.text("'2'"), autoincrement=False, nullable=False),
    sa.Column('applied', mysql.BIGINT(), server_default=sa.text("'0'"), autoincrement=False, nullable=False),
    sa.Column('total', mysql.BIGINT(), server_default=sa.text("'0'"), autoincrement=False, nullable=False),
    sa.Column('executed_at', mysql.TIMESTAMP(), nullable=False),
    sa.Column('execution_time', mysql.BIGINT(), autoincrement=False, nullable=False),
    sa.Column('error', mysql.LONGTEXT(collation='utf8mb4_bin'), nullable=True),
    sa.Column('error_stmt', mysql.LONGTEXT(collation='utf8mb4_bin'), nullable=True),
    sa.Column('hash', mysql.VARCHAR(collation='utf8mb4_bin', length=255), nullable=False),
    sa.Column('partial_hashes', mysql.JSON(), nullable=True),
    sa.Column('operator_version', mysql.VARCHAR(collation='utf8mb4_bin', length=255), nullable=False),
    sa.PrimaryKeyConstraint('version'),
    mysql_collate='utf8mb4_bin',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.drop_index(op.f('ix_task_contents_task_id'), table_name='task_contents')
    op.drop_index(op.f('ix_task_contents_ref_lesson_id'), table_name='task_contents')
    op.drop_index(op.f('ix_task_contents_ref_book_id'), table_name='task_contents')
    op.drop_index(op.f('ix_task_contents_id'), table_name='task_contents')
    op.drop_index(op.f('ix_task_contents_content_type'), table_name='task_contents')
    op.drop_table('task_contents')
    op.drop_index(op.f('ix_tasks_textbook_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_teacher_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_subject'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_lesson_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_class_id'), table_name='tasks')
    op.drop_table('tasks')
    op.drop_table('task_target')
    op.drop_index(op.f('ix_submissions_student_task_id'), table_name='submissions')
    op.drop_index(op.f('ix_submissions_student_id'), table_name='submissions')
    op.drop_index(op.f('ix_submissions_is_correct'), table_name='submissions')
    op.drop_index(op.f('ix_submissions_id'), table_name='submissions')
    op.drop_index(op.f('ix_submissions_content_id'), table_name='submissions')
    op.drop_table('submissions')
    op.drop_index(op.f('ix_classes_teacher_id'), table_name='classes')
    op.drop_index(op.f('ix_classes_subject'), table_name='classes')
    op.drop_index(op.f('ix_classes_status'), table_name='classes')
    op.drop_index(op.f('ix_classes_school_name'), table_name='classes')
    op.drop_index(op.f('ix_classes_id'), table_name='classes')
    op.drop_index(op.f('ix_classes_grade_level'), table_name='classes')
    op.drop_index(op.f('ix_classes_class_code'), table_name='classes')
    op.drop_table('classes')
    op.drop_index(op.f('ix_class_teachers_teacher_id'), table_name='class_teachers')
    op.drop_index(op.f('ix_class_teachers_status'), table_name='class_teachers')
    op.drop_index(op.f('ix_class_teachers_id'), table_name='class_teachers')
    op.drop_index(op.f('ix_class_teachers_class_id'), table_name='class_teachers')
    op.drop_table('class_teachers')
    op.drop_index(op.f('ix_class_students_student_id'), table_name='class_students')
    op.drop_index(op.f('ix_class_students_status'), table_name='class_students')
    op.drop_index(op.f('ix_class_students_id'), table_name='class_students')
    op.drop_index(op.f('ix_class_students_class_id'), table_name='class_students')
    op.drop_table('class_students')
    # ### end Alembic commands ###
